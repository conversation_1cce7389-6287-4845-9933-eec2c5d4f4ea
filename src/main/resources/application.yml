# Confluence MCP Server 配置
server:
  port: 8080

# Confluence 服务器配置
confluence:
  base-url: https://doc.greatld.com  # 替换为你的Confluence服务器地址
  username: wangjp                             # 替换为你的用户名
  password: qqqq1111                             # 替换为你的密码或API Token
  connect-timeout: 30000                      # 连接超时时间（毫秒）
  read-timeout: 60000                         # 读取超时时间（毫秒）
  ssl-verification: true                      # 是否启用SSL验证

# MCP 服务器配置
mcp:
  server-name: "Confluence MCP Server"
  server-version: "1.0.0"
  websocket-port: 8081
  websocket-path: "/mcp"
  resources-enabled: true
  tools-enabled: true
  prompts-enabled: true
  resource-subscription-enabled: true
  resource-list-changed-enabled: true

# 日志配置
logging:
  level:
    com.confluencemcpserver: DEBUG
    org.springframework.web.socket: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Spring Boot 配置
spring:
  application:
    name: confluence-mcp-server
  main:
    web-application-type: servlet
