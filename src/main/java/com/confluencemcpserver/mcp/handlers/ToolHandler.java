package com.confluencemcpserver.mcp.handlers;

import com.confluencemcpserver.confluence.ConfluenceClient;
import com.confluencemcpserver.confluence.model.*;
import com.confluencemcpserver.dto.McpTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MCP工具处理器
 */
@Component
public class ToolHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(ToolHandler.class);
    
    private final ConfluenceClient confluenceClient;

    @Autowired
    public ToolHandler(ConfluenceClient confluenceClient) {
        this.confluenceClient = confluenceClient;
    }

    /**
     * 列出所有工具
     */
    public List<McpTool> listTools() {
        List<McpTool> tools = new ArrayList<>();
        
        // 搜索页面工具
        tools.add(createSearchPagesSchema());
        
        // 获取页面内容工具
        tools.add(createGetPageContentSchema());
        
        // 列出附件工具
        tools.add(createListAttachmentsSchema());
        
        // 获取页面链接工具
        tools.add(createGetPageLinksSchema());
        
        // 列出空间工具
        tools.add(createListSpacesSchema());
        
        // 获取评论工具
        tools.add(createGetCommentsSchema());
        
        logger.debug("列出 {} 个工具", tools.size());
        return tools;
    }

    /**
     * 调用工具
     */
    public Mono<Map<String, Object>> callTool(String name, Map<String, Object> arguments) {
        logger.debug("调用工具: {} 参数: {}", name, arguments);
        
        switch (name) {
            case "search_pages":
                return searchPages(arguments);
            case "get_page_content":
                return getPageContent(arguments);
            case "list_attachments":
                return listAttachments(arguments);
            case "get_page_links":
                return getPageLinks(arguments);
            case "list_spaces":
                return listSpaces(arguments);
            case "get_comments":
                return getComments(arguments);
            default:
                return Mono.error(new IllegalArgumentException("未知工具: " + name));
        }
    }

    /**
     * 搜索页面
     */
    private Mono<Map<String, Object>> searchPages(Map<String, Object> arguments) {
        String query = (String) arguments.get("query");
        String spaceKey = (String) arguments.get("spaceKey");
        Integer limit = (Integer) arguments.getOrDefault("limit", 10);
        
        return confluenceClient.searchPages(query, spaceKey, 0, limit)
                .map(pageList -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("content", List.of(Map.of(
                            "type", "text",
                            "text", formatPageListResult(pageList)
                    )));
                    result.put("isError", false);
                    return result;
                })
                .onErrorReturn(createErrorResult("搜索页面失败"));
    }

    /**
     * 获取页面内容
     */
    private Mono<Map<String, Object>> getPageContent(Map<String, Object> arguments) {
        String pageId = (String) arguments.get("pageId");
        
        return confluenceClient.getPage(pageId, "body.storage,body.view,space,version,history")
                .map(page -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("content", List.of(Map.of(
                            "type", "text",
                            "text", formatPageContent(page)
                    )));
                    result.put("isError", false);
                    return result;
                })
                .onErrorReturn(createErrorResult("获取页面内容失败"));
    }

    /**
     * 列出附件
     */
    private Mono<Map<String, Object>> listAttachments(Map<String, Object> arguments) {
        String pageId = (String) arguments.get("pageId");
        Integer limit = (Integer) arguments.getOrDefault("limit", 10);
        
        return confluenceClient.getPageAttachments(pageId, 0, limit)
                .map(attachmentList -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("content", List.of(Map.of(
                            "type", "text",
                            "text", formatAttachmentListResult(attachmentList)
                    )));
                    result.put("isError", false);
                    return result;
                })
                .onErrorReturn(createErrorResult("获取附件列表失败"));
    }

    /**
     * 获取页面链接
     */
    private Mono<Map<String, Object>> getPageLinks(Map<String, Object> arguments) {
        String pageId = (String) arguments.get("pageId");
        
        return confluenceClient.getPage(pageId, "body.storage,body.view")
                .map(page -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("content", List.of(Map.of(
                            "type", "text",
                            "text", extractLinksFromPage(page)
                    )));
                    result.put("isError", false);
                    return result;
                })
                .onErrorReturn(createErrorResult("获取页面链接失败"));
    }

    /**
     * 列出空间
     */
    private Mono<Map<String, Object>> listSpaces(Map<String, Object> arguments) {
        Integer limit = (Integer) arguments.getOrDefault("limit", 20);
        
        return confluenceClient.getAllSpaces(0, limit)
                .map(spaces -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("content", List.of(Map.of(
                            "type", "text",
                            "text", formatSpaceListResult(spaces)
                    )));
                    result.put("isError", false);
                    return result;
                })
                .onErrorReturn(createErrorResult("获取空间列表失败"));
    }

    /**
     * 获取评论
     */
    private Mono<Map<String, Object>> getComments(Map<String, Object> arguments) {
        String pageId = (String) arguments.get("pageId");
        Integer limit = (Integer) arguments.getOrDefault("limit", 10);
        
        return confluenceClient.getPageComments(pageId, 0, limit)
                .map(commentList -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("content", List.of(Map.of(
                            "type", "text",
                            "text", formatCommentListResult(commentList)
                    )));
                    result.put("isError", false);
                    return result;
                })
                .onErrorReturn(createErrorResult("获取评论失败"));
    }

    /**
     * 创建搜索页面工具模式
     */
    private McpTool createSearchPagesSchema() {
        Map<String, Object> schema = Map.of(
                "type", "object",
                "properties", Map.of(
                        "query", Map.of(
                                "type", "string",
                                "description", "搜索关键词"
                        ),
                        "spaceKey", Map.of(
                                "type", "string",
                                "description", "空间键（可选）"
                        ),
                        "limit", Map.of(
                                "type", "integer",
                                "description", "返回结果数量限制",
                                "default", 10
                        )
                ),
                "required", List.of("query")
        );
        
        return new McpTool("search_pages", "🔍 搜索页面", "在Confluence中搜索页面", schema);
    }

    /**
     * 创建获取页面内容工具模式
     */
    private McpTool createGetPageContentSchema() {
        Map<String, Object> schema = Map.of(
                "type", "object",
                "properties", Map.of(
                        "pageId", Map.of(
                                "type", "string",
                                "description", "页面ID"
                        )
                ),
                "required", List.of("pageId")
        );
        
        return new McpTool("get_page_content", "📄 获取页面内容", "获取指定页面的详细内容", schema);
    }

    /**
     * 创建列出附件工具模式
     */
    private McpTool createListAttachmentsSchema() {
        Map<String, Object> schema = Map.of(
                "type", "object",
                "properties", Map.of(
                        "pageId", Map.of(
                                "type", "string",
                                "description", "页面ID"
                        ),
                        "limit", Map.of(
                                "type", "integer",
                                "description", "返回结果数量限制",
                                "default", 10
                        )
                ),
                "required", List.of("pageId")
        );
        
        return new McpTool("list_attachments", "📎 列出附件", "列出页面的所有附件", schema);
    }

    /**
     * 创建获取页面链接工具模式
     */
    private McpTool createGetPageLinksSchema() {
        Map<String, Object> schema = Map.of(
                "type", "object",
                "properties", Map.of(
                        "pageId", Map.of(
                                "type", "string",
                                "description", "页面ID"
                        )
                ),
                "required", List.of("pageId")
        );
        
        return new McpTool("get_page_links", "🔗 获取页面链接", "提取页面中的所有链接", schema);
    }

    /**
     * 创建列出空间工具模式
     */
    private McpTool createListSpacesSchema() {
        Map<String, Object> schema = Map.of(
                "type", "object",
                "properties", Map.of(
                        "limit", Map.of(
                                "type", "integer",
                                "description", "返回结果数量限制",
                                "default", 20
                        )
                )
        );
        
        return new McpTool("list_spaces", "📁 列出空间", "列出所有可访问的Confluence空间", schema);
    }

    /**
     * 创建获取评论工具模式
     */
    private McpTool createGetCommentsSchema() {
        Map<String, Object> schema = Map.of(
                "type", "object",
                "properties", Map.of(
                        "pageId", Map.of(
                                "type", "string",
                                "description", "页面ID"
                        ),
                        "limit", Map.of(
                                "type", "integer",
                                "description", "返回结果数量限制",
                                "default", 10
                        )
                ),
                "required", List.of("pageId")
        );
        
        return new McpTool("get_comments", "💬 获取评论", "获取页面的所有评论", schema);
    }

    /**
     * 格式化页面列表结果
     */
    private String formatPageListResult(PageList pageList) {
        StringBuilder result = new StringBuilder();
        result.append("搜索结果 (共 ").append(pageList.getSize()).append(" 个页面):\n\n");
        
        for (Page page : pageList.getResults()) {
            result.append("📄 ").append(page.getTitle()).append("\n");
            result.append("   ID: ").append(page.getId()).append("\n");
            if (page.getSpace() != null) {
                result.append("   空间: ").append(page.getSpace().getName()).append("\n");
            }
            if (page.getBody() != null && page.getBody().getView() != null) {
                String content = page.getBody().getView().getValue();
                if (content.length() > 200) {
                    content = content.substring(0, 200) + "...";
                }
                result.append("   摘要: ").append(content.replaceAll("<[^>]*>", "")).append("\n");
            }
            result.append("\n");
        }
        
        return result.toString();
    }

    /**
     * 格式化页面内容
     */
    private String formatPageContent(Page page) {
        StringBuilder result = new StringBuilder();
        result.append("页面标题: ").append(page.getTitle()).append("\n");
        result.append("页面ID: ").append(page.getId()).append("\n");
        
        if (page.getSpace() != null) {
            result.append("所属空间: ").append(page.getSpace().getName()).append("\n");
        }
        
        if (page.getVersion() != null) {
            result.append("版本: ").append(page.getVersion().getNumber()).append("\n");
            if (page.getVersion().getWhen() != null) {
                result.append("最后修改: ").append(page.getVersion().getWhen()).append("\n");
            }
        }
        
        result.append("\n页面内容:\n");
        result.append("=" .repeat(50)).append("\n");
        
        if (page.getBody() != null && page.getBody().getView() != null) {
            result.append(page.getBody().getView().getValue());
        } else if (page.getBody() != null && page.getBody().getStorage() != null) {
            result.append(page.getBody().getStorage().getValue());
        }
        
        return result.toString();
    }

    /**
     * 格式化附件列表结果
     */
    private String formatAttachmentListResult(AttachmentList attachmentList) {
        StringBuilder result = new StringBuilder();
        result.append("附件列表 (共 ").append(attachmentList.getSize()).append(" 个附件):\n\n");
        
        for (Attachment attachment : attachmentList.getResults()) {
            result.append("📎 ").append(attachment.getTitle()).append("\n");
            result.append("   ID: ").append(attachment.getId()).append("\n");
            
            if (attachment.getExtensions() != null) {
                if (attachment.getExtensions().getMediaType() != null) {
                    result.append("   类型: ").append(attachment.getExtensions().getMediaType()).append("\n");
                }
                if (attachment.getExtensions().getFileSize() > 0) {
                    result.append("   大小: ").append(formatFileSize(attachment.getExtensions().getFileSize())).append("\n");
                }
            }
            
            if (attachment.getLinks() != null && attachment.getLinks().getDownload() != null) {
                result.append("   下载链接: ").append(attachment.getLinks().getDownload()).append("\n");
            }
            
            result.append("\n");
        }
        
        return result.toString();
    }

    /**
     * 提取页面中的链接
     */
    private String extractLinksFromPage(Page page) {
        StringBuilder result = new StringBuilder();
        result.append("页面链接提取结果:\n\n");
        
        String content = "";
        if (page.getBody() != null && page.getBody().getStorage() != null) {
            content = page.getBody().getStorage().getValue();
        } else if (page.getBody() != null && page.getBody().getView() != null) {
            content = page.getBody().getView().getValue();
        }
        
        // 提取HTTP链接
        Pattern httpPattern = Pattern.compile("https?://[\\w\\-._~:/?#\\[\\]@!$&'()*+,;=%]+");
        Matcher httpMatcher = httpPattern.matcher(content);
        
        Set<String> links = new HashSet<>();
        while (httpMatcher.find()) {
            links.add(httpMatcher.group());
        }
        
        // 提取Confluence内部链接
        Pattern confluencePattern = Pattern.compile("<ri:page ri:content-title=\"([^\"]+)\"");
        Matcher confluenceMatcher = confluencePattern.matcher(content);
        
        while (confluenceMatcher.find()) {
            links.add("内部页面: " + confluenceMatcher.group(1));
        }
        
        if (links.isEmpty()) {
            result.append("未找到链接");
        } else {
            result.append("找到 ").append(links.size()).append(" 个链接:\n\n");
            for (String link : links) {
                result.append("🔗 ").append(link).append("\n");
            }
        }
        
        return result.toString();
    }

    /**
     * 格式化空间列表结果
     */
    private String formatSpaceListResult(List<Space> spaces) {
        StringBuilder result = new StringBuilder();
        result.append("空间列表 (共 ").append(spaces.size()).append(" 个空间):\n\n");
        
        for (Space space : spaces) {
            result.append("📁 ").append(space.getName()).append("\n");
            result.append("   键: ").append(space.getKey()).append("\n");
            result.append("   类型: ").append(space.getType()).append("\n");
            
            if (space.getDescription() != null && space.getDescription().getPlain() != null) {
                String desc = space.getDescription().getPlain().getValue();
                if (desc.length() > 100) {
                    desc = desc.substring(0, 100) + "...";
                }
                result.append("   描述: ").append(desc).append("\n");
            }
            
            result.append("\n");
        }
        
        return result.toString();
    }

    /**
     * 格式化评论列表结果
     */
    private String formatCommentListResult(CommentList commentList) {
        StringBuilder result = new StringBuilder();
        result.append("评论列表 (共 ").append(commentList.getSize()).append(" 个评论):\n\n");
        
        for (Comment comment : commentList.getResults()) {
            result.append("💬 ").append(comment.getTitle()).append("\n");
            result.append("   ID: ").append(comment.getId()).append("\n");
            
            if (comment.getBody() != null && comment.getBody().getView() != null) {
                String content = comment.getBody().getView().getValue().replaceAll("<[^>]*>", "");
                if (content.length() > 200) {
                    content = content.substring(0, 200) + "...";
                }
                result.append("   内容: ").append(content).append("\n");
            }
            
            result.append("\n");
        }
        
        return result.toString();
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }

    /**
     * 创建错误结果
     */
    private Map<String, Object> createErrorResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("content", List.of(Map.of(
                "type", "text",
                "text", message
        )));
        result.put("isError", true);
        return result;
    }
}
