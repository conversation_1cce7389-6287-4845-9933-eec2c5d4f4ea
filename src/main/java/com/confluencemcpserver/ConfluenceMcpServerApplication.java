package com.confluencemcpserver;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Confluence MCP服务器主应用类
 *
 * 这是一个基于Model Context Protocol (MCP)的Confluence集成服务器，
 * 为AI助手提供访问本地Confluence实例的能力。
 *
 * 主要功能：
 * - 获取页面内容（文本、HTML格式）
 * - 获取附件信息和下载链接
 * - 获取页面中的超链接
 * - 搜索页面和空间
 * - 获取页面评论
 * - 浏览空间结构
 */
@SpringBootApplication
@EnableConfigurationProperties
public class ConfluenceMcpServerApplication {

    private static final Logger logger = LoggerFactory.getLogger(ConfluenceMcpServerApplication.class);

    public static void main(String[] args) {
        logger.info("启动Confluence MCP服务器...");

        SpringApplication app = new SpringApplication(ConfluenceMcpServerApplication.class);
        app.run(args);

        logger.info("Confluence MCP服务器启动完成");
        logger.info("WebSocket端点: ws://localhost:8080/mcp");
        logger.info("请确保已正确配置Confluence服务器连接信息");
    }
}
